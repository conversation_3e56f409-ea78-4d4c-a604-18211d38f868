// Game state variables
let currentQuestion = null;
let score = 0;
let timer = null;
let timeLeft = 45; // 45 seconds per question (changed from 30)
let questions = [];
let incorrectQuestions = []; // Store questions that were answered incorrectly
let isReviewingIncorrectQuestions = false; // Flag to track if we're reviewing incorrect questions
let currentQuestionIndex = 0;
let totalLives = 0;
let currentLives = 0;
let canUseAddTime = true;
let canUseShowAnswer = true;
let totalExpEarned = 0; // Track total EXP earned in this session
let gameActive = false; // Track if game is active

// Sound Effects
const correctSound = new Audio('../../sounds/correct2.mp3');
const wrongSound = new Audio('../../sounds/wrong2.mp3');
const successSound = new Audio('../../sounds/success.mp3');

// Set initial volume
correctSound.volume = 0.3;
wrongSound.volume = 0.3;
successSound.volume = 0.3;

// Modal handling
const instructionModal = document.getElementById('instruction-modal');
const resultsModal = document.getElementById('results-modal');
const startBtn = document.getElementById('start-btn');
const restartBtn = document.getElementById('restart-btn');
const mainMenuBtn = document.getElementById('main-menu-btn');
const resultTitle = document.getElementById('result-title');
const resultMessage = document.getElementById('result-message');
const resultStars = document.getElementById('result-stars');
const expText = document.querySelector('.exp-text');

// Show instruction modal on page load
window.addEventListener('DOMContentLoaded', () => {
    instructionModal.classList.add('show');
});

// Start game when start button is clicked
startBtn.addEventListener('click', () => {
    instructionModal.classList.remove('show');
    startGame();
});

// Restart game
restartBtn.addEventListener('click', () => {
    // Add a fade-out transition to the game container
    const gameContainer = document.querySelector('.game-container');
    gameContainer.classList.add('fade-transition');
    
    // Create and show loading spinner
    let spinner = document.querySelector('.loading-spinner');
    if (!spinner) {
        spinner = document.createElement('div');
        spinner.className = 'loading-spinner';
        gameContainer.appendChild(spinner);
    }
    setTimeout(() => spinner.classList.add('show'), 50);
    
    // Hide the results modal with a smooth transition
    resultsModal.classList.remove('show');
    
    // Wait for fade-out animation to complete
    setTimeout(() => {
        // Complete game reset - ensure all visual effects and state are cleared
        completeGameReset();
        
        // Start the game
        startGame();
        
        // Fade back in with a nice animation
        setTimeout(() => {
            // Hide spinner
            spinner.classList.remove('show');
            
            // Show game content
            gameContainer.classList.remove('fade-transition');
            gameContainer.classList.add('fade-in');
            
            // Remove the animation class after it completes
            setTimeout(() => {
                gameContainer.classList.remove('fade-in');
                
                // Remove spinner element
                if (spinner && spinner.parentNode) {
                    spinner.parentNode.removeChild(spinner);
                }
                
                console.log('Game restarted successfully with smooth transition');
            }, 500);
        }, 100);
    }, 800); // Slightly longer delay for better user experience
});

// Return to main menu
mainMenuBtn.addEventListener('click', () => {
    window.location.href = '../mainpage.html';
});

// Initialize the game when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Add level data attribute to body for savetodb.php
    const urlParams = new URLSearchParams(window.location.search);
    const level = urlParams.get('level') || 1;
    document.body.dataset.level = level;
    
    // Load questions from the server
    loadQuestions();
    
    // Set up button handlers
    document.getElementById('exitBtn').addEventListener('click', () => window.location.href = '../mainpage.html');
    document.getElementById('addTimeBtn').addEventListener('click', useAddTimePowerup);
    document.getElementById('showAnswerBtn').addEventListener('click', useShowAnswerPowerup);
});

// Load questions from the server
function loadQuestions() {
    // Get the level from URL parameters or default to 1
    const urlParams = new URLSearchParams(window.location.search);
    const level = urlParams.get('level') || 1;

    console.log('Attempting to load questions for level:', level);

    fetch(`../../php/game.php?level=${level}`)
        .then(response => response.text())
        .then(text => {
            try {
                const response = JSON.parse(text);
                if (response.success && response.data) {
                    // Filter for matching type questions
                    questions = response.data.filter(q => {
                        // For matching type, we only need question_text and correct_answer
                        return q.question_text && q.correct_answer;
                    });
                    
                    if (questions.length > 0) {
                        // Calculate total lives as 20% of total questions, rounded up
                        totalLives = Math.ceil(questions.length * 0.2);
                        currentLives = totalLives;
                        updateLivesDisplay();
                        initializeDragAndDrop();
                        displayQuestion(0);
                    } else {
                        const errorMsg = `No valid questions found for level ${level}. Please make sure questions exist in the database.`;
                        console.error(errorMsg);
                        document.getElementById('questionText').textContent = errorMsg;
                    }
                } else {
                    throw new Error('Invalid response format or no data received from server');
                }
            } catch (e) {
                console.error('Error processing response:', e);
                document.getElementById('questionText').textContent = 'Error loading questions. Please try again later.';
            }
        })
        .catch(error => {
            console.error('Network or processing error:', error);
            document.getElementById('questionText').textContent = 'Error loading questions. Please try again later.';
        });
}

// Initialize drag and drop functionality
function initializeDragAndDrop() {
    const dropZone = document.getElementById('dropZone');
    if (!dropZone) return;
    
    // Prevent default drag behaviors
    document.addEventListener('dragover', e => e.preventDefault());
    document.addEventListener('drop', e => e.preventDefault());
    
    // Add drop zone handlers
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('active');
    });
    
    dropZone.addEventListener('dragleave', function() {
        this.classList.remove('active');
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('active');
        const draggedElement = document.querySelector('.dragging');
        if (draggedElement) {
            handleDrop(draggedElement);
        }
    });
}

// Make an element draggable
function makeDraggable(element) {
    element.draggable = true;
    element.addEventListener('dragstart', function() {
        this.classList.add('dragging');
    });
    element.addEventListener('dragend', function() {
        this.classList.remove('dragging');
    });
}

// Display a question
function displayQuestion(index) {
    if (!questions[index]) return;
    
    currentQuestion = questions[index];
    currentQuestionIndex = index;
    
    // Display question text
    document.getElementById('questionText').textContent = currentQuestion.question_text;
    
    // Clear previous options
    const draggableOptions = document.getElementById('draggableOptions');
    draggableOptions.innerHTML = '';
    
    // Get unique options using Set
    const uniqueOptions = new Set(questions.map(q => q.correct_answer));
    const options = shuffleArray([...uniqueOptions]);
    
    // Create draggable elements
    options.forEach(option => {
        if (option && option.trim()) {
            const optionElement = document.createElement('div');
            optionElement.className = 'option-item';
            optionElement.dataset.value = option;
            optionElement.textContent = option;
            makeDraggable(optionElement);
            draggableOptions.appendChild(optionElement);
        }
    });
    
    // Reset timer for this question
    startTimer();
}

// Handle drop event
function handleDrop(draggedElement) {
    // Don't process drops if game is not active
    if (!gameActive) return;
    
    const dropZone = document.getElementById('dropZone');
    const answer = draggedElement.dataset.value;
    const correctAnswer = currentQuestion.correct_answer;
    
    if (answer === correctAnswer) {
        handleCorrectAnswer();
    } else {
        handleWrongAnswer();
    }
}

// Update lives display
function updateLivesDisplay() {
    const livesDisplay = document.getElementById('livesDisplay');
    livesDisplay.innerHTML = '';
    for (let i = 0; i < totalLives; i++) {
        const lifeIcon = document.createElement('span');
        lifeIcon.textContent = '❤️';
        lifeIcon.className = `life-icon ${i >= currentLives ? 'lost' : ''}`;
        livesDisplay.appendChild(lifeIcon);
    }
}

// Timer functions
function startTimer() {
    // Clear any existing timer
    if (timer) {
        clearInterval(timer);
        timer = null;
    }
    
    // Reset time left
    timeLeft = 45; // Changed from 30 to 45 seconds
    updateTimerDisplay();
    
    // Start new timer
    timer = setInterval(function() {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            handleTimeUp();
        }
    }, 1000);
}

function updateTimerDisplay() {
    const timerElement = document.getElementById('timer');
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    timerElement.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
}

// Game control functions
function startGame() {
    // Make sure we have questions loaded
    if (questions.length === 0) {
        loadQuestions();
        return;
    }
    
    // Set game as active
    gameActive = true;
    
    // Reset game state
    currentQuestionIndex = 0;
    totalExpEarned = 0;
    
    // Display first question
    displayQuestion(0);
    
    // Timer is now started in displayQuestion
}

// Complete game reset function
function completeGameReset() {
    // Clear any existing timer
    if (timer) {
        clearInterval(timer);
        timer = null;
    }
    
    // Reset game state variables
    currentQuestion = null;
    score = 0;
    timeLeft = 45;
    currentQuestionIndex = 0;
    totalExpEarned = 0;
    gameActive = false;
    incorrectQuestions = [];
    isReviewingIncorrectQuestions = false;
    
    // Reset power-ups
    canUseAddTime = true;
    canUseShowAnswer = true;
    document.getElementById('addTimeBtn').disabled = false;
    document.getElementById('showAnswerBtn').disabled = false;
    
    // Reset lives if we have questions loaded
    if (questions.length > 0) {
        totalLives = Math.ceil(questions.length * 0.2);
        currentLives = totalLives;
        updateLivesDisplay();
    }
    
    // Shuffle the questions to get a fresh experience
    if (questions.length > 0) {
        questions = shuffleArray([...questions]);
    }
    
    // Clear the game area
    document.getElementById('draggableOptions').innerHTML = '';
    document.getElementById('questionText').textContent = '';
    
    // Reset drop zone completely - both class and inline styles
    const dropZone = document.getElementById('dropZone');
    dropZone.textContent = 'Drop your answer here';
    dropZone.className = 'drop-zone';
    
    // Reset all inline styles that might have been added
    dropZone.style.backgroundColor = '';
    dropZone.style.boxShadow = '';
    dropZone.style.border = '';
    dropZone.style.color = '';
    dropZone.style.transition = '';
    dropZone.style.transform = '';
    dropZone.style.opacity = '';
    
    // Remove any EXP indicators, sparkles, or other added elements
    const expIndicators = document.querySelectorAll('.exp-indicator');
    expIndicators.forEach(indicator => indicator.remove());
    
    const expSparkles = document.querySelectorAll('.exp-sparkle');
    expSparkles.forEach(sparkle => sparkle.remove());
    
    // Remove any review messages that might still be on screen
    const reviewMessages = document.querySelectorAll('.review-message');
    reviewMessages.forEach(message => message.remove());
    
    // Reset any highlighted options
    const options = document.querySelectorAll('.option-item');
    options.forEach(option => {
        option.classList.remove('highlight', 'dragging');
        option.style = ''; // Reset any inline styles
    });
    
    // Update timer display
    updateTimerDisplay();
    
    // Make sure the game container is clean from any added elements
    const gameContainer = document.querySelector('.game-container');
    Array.from(gameContainer.children).forEach(child => {
        if (!child.classList.contains('game-layout') && 
            !child.classList.contains('game-header') && 
            !child.id === 'instruction-modal' && 
            !child.id === 'results-modal') {
            child.remove();
        }
    });
    
    console.log('Game completely reset and ready to start again');
}

function resetGame() {
    // Clear any existing timer
    if (timer) {
        clearInterval(timer);
        timer = null;
    }
    
    // Reset game state
    currentQuestionIndex = 0;
    timeLeft = 45;
    totalExpEarned = 0;
    incorrectQuestions = [];
    isReviewingIncorrectQuestions = false;
    updateTimerDisplay();
    
    // Reset lives
    currentLives = totalLives;
    updateLivesDisplay();
    
    // Reset power-ups
    document.getElementById('addTimeBtn').disabled = false;
    document.getElementById('showAnswerBtn').disabled = false;
    canUseAddTime = true;
    canUseShowAnswer = true;
    
    // Clear the game area
    document.getElementById('draggableOptions').innerHTML = '';
    document.getElementById('questionText').textContent = '';
    
    // Reset drop zone completely - both class and inline styles
    const dropZone = document.getElementById('dropZone');
    dropZone.textContent = 'Drop your answer here';
    dropZone.className = 'drop-zone';
    
    // Reset all inline styles that might have been added
    dropZone.style.backgroundColor = '';
    dropZone.style.boxShadow = '';
    dropZone.style.border = '';
    dropZone.style.color = '';
    dropZone.style.transition = '';
    dropZone.style.transform = '';
    dropZone.style.opacity = '';
    
    // Remove any EXP indicators, sparkles, or other added elements
    const expIndicators = document.querySelectorAll('.exp-indicator');
    expIndicators.forEach(indicator => indicator.remove());
    
    const expSparkles = document.querySelectorAll('.exp-sparkle');
    expSparkles.forEach(sparkle => sparkle.remove());
    
    // Remove any review messages that might still be on screen
    const reviewMessages = document.querySelectorAll('.review-message');
    reviewMessages.forEach(message => message.remove());
    
    // Reset any highlighted options
    const options = document.querySelectorAll('.option-item');
    options.forEach(option => {
        option.classList.remove('highlight', 'dragging');
        option.style = ''; // Reset any inline styles
    });
    
    // Make sure the game container is clean from any added elements
    const gameContainer = document.querySelector('.game-container');
    Array.from(gameContainer.children).forEach(child => {
        if (!child.classList.contains('game-layout') && 
            !child.classList.contains('game-header') && 
            !child.id === 'instruction-modal' && 
            !child.id === 'results-modal') {
            child.remove();
        }
    });
}

// Power-ups
function useAddTimePowerup() {
    // Don't allow power-ups if game is not active
    if (!gameActive || !canUseAddTime) return;
    
    timeLeft += 10;
    updateTimerDisplay();
    canUseAddTime = false;
    document.getElementById('addTimeBtn').disabled = true;
}

function useShowAnswerPowerup() {
    // Don't allow power-ups if game is not active
    if (!gameActive || !canUseShowAnswer) return;
    
    const correctAnswer = currentQuestion.correct_answer;
    const options = document.querySelectorAll('.option-item');
    
    options.forEach(option => {
        if (option.dataset.value === correctAnswer) {
            option.classList.add('highlight');
            setTimeout(() => option.classList.remove('highlight'), 300);
        }
    });
    
    canUseShowAnswer = false;
    document.getElementById('showAnswerBtn').disabled = true;
}

// Game end conditions
function handleGameEnd() {
    // Clear the timer
    if (timer) {
        clearInterval(timer);
        timer = null;
    }
    
    // Check if we have incorrect questions to review
    if (!isReviewingIncorrectQuestions && incorrectQuestions.length > 0 && currentLives > 0) {
        // Switch to reviewing incorrect questions
        isReviewingIncorrectQuestions = true;
        
        // Replace questions with incorrectQuestions for the review phase
        questions = [...incorrectQuestions];
        incorrectQuestions = []; // Clear the array for potential new incorrect answers
        
        // Reset question index
        currentQuestionIndex = 0;
        
        // Show a message to the user that they're now reviewing incorrect questions
        showReviewMessage();
        
        // Show the first incorrect question
        displayQuestion(0);
        
        // Keep game active
        gameActive = true;
        
        return;
    }
    
    // Set game as inactive
    gameActive = false;
    
    const remainingLives = currentLives;
    const success = remainingLives > 0 && currentQuestionIndex >= questions.length - 1;
    showResults(success);
}

function handleWrongAnswer() {
    // Play wrong sound
    wrongSound.play();
    
    // Decrease lives
    currentLives--;
    updateLivesDisplay();
    
    // Show feedback with glow effect instead of just color change
    const dropZone = document.getElementById('dropZone');
    
    // Add glow effect for wrong answer
    dropZone.style.backgroundColor = 'rgba(244, 67, 54, 0.3)'; // Light red background
    dropZone.style.boxShadow = '0 0 20px 5px rgba(244, 67, 54, 0.7)'; // Red glow
    dropZone.style.border = '2px solid #F44336'; // Bright red border
    dropZone.style.transition = 'all 0.3s ease'; // Smooth transition
    
    // Store the incorrect question to ask again later
    if (!isReviewingIncorrectQuestions) {
        // Make a deep copy of the current question to avoid reference issues
        const questionCopy = JSON.parse(JSON.stringify(currentQuestion));
        incorrectQuestions.push(questionCopy);
    }
    
    // Check if game over due to no more lives
    if (currentLives <= 0) {
        setTimeout(() => {
            handleGameEnd();
        }, 4500);
        return;
    }
    
    // Continue to next question
    setTimeout(() => {
        // Reset drop zone appearance
        dropZone.style.backgroundColor = '';
        dropZone.style.boxShadow = '';
        dropZone.style.border = '';
        dropZone.textContent = 'Drop your answer here';
        
        currentQuestionIndex++;
        if (currentQuestionIndex >= questions.length) {
            // Check if we have incorrect questions to review
            if (!isReviewingIncorrectQuestions && incorrectQuestions.length > 0) {
                isReviewingIncorrectQuestions = true;
                questions = [...incorrectQuestions];
                incorrectQuestions = [];
                currentQuestionIndex = 0;
                showReviewMessage();
                displayQuestion(0);
            } else {
                handleGameEnd();
            }
        } else {
            displayQuestion(currentQuestionIndex);
        }
    }, 4500);
}

function handleCorrectAnswer() {
    // Play correct sound
    correctSound.play();
    
    // Randomize exp between 10-20 for correct answer
    const randomExp = Math.floor(Math.random() * 11) + 10; // Random number between 10-20
    // Add time bonus: 1 EXP per 3 seconds left
    const timeBonus = Math.floor(timeLeft / 3);
    const questionExp = randomExp + timeBonus;
    totalExpEarned += questionExp;
    
    // Show feedback with glow effect instead of just color change
    const dropZone = document.getElementById('dropZone');
    
    // Add glow effect for correct answer
    dropZone.style.backgroundColor = 'rgba(76, 175, 80, 0.3)'; // Light green background
    dropZone.style.boxShadow = '0 0 20px 5px rgba(76, 175, 80, 0.7)'; // Green glow
    dropZone.style.border = '2px solid #4CAF50'; // Bright green border
    dropZone.style.transition = 'all 0.3s ease'; // Smooth transition
    
    // Enhanced EXP indicator with animation
    showExpGain(questionExp, dropZone);
    
    // Send EXP to database immediately for this question
    fetch('../../php/savetodb.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            expEarned: questionExp,
            score: 0, // We'll update the final score at the end
            starsCount: 0, // We'll update stars at the end
            level: parseInt(document.body.dataset.level) || 1
        })
    }).catch(error => {
        console.error('Error saving question EXP:', error);
    });
    
    // Proceed to next question or end game
    setTimeout(() => {
        // Reset drop zone appearance
        dropZone.style.backgroundColor = '';
        dropZone.style.boxShadow = '';
        dropZone.style.border = '';
        dropZone.textContent = 'Drop your answer here';
        
        currentQuestionIndex++;
        if (currentQuestionIndex >= questions.length) {
            handleGameEnd();
        } else {
            displayQuestion(currentQuestionIndex);
        }
    }, 4500);
}

// Add this new function to show EXP gain visual effect
function showExpGain(amount, targetElement = null) {
    // Create EXP indicator element
    const expIndicator = document.createElement('div');
    expIndicator.className = 'exp-indicator';
    expIndicator.textContent = `+${amount} EXP`;
    
    // Style the indicator
    expIndicator.style.position = 'absolute';
    
    if (targetElement) {
        // Position relative to the target element
        const rect = targetElement.getBoundingClientRect();
        const gameContainer = document.querySelector('.game-container');
        const gameRect = gameContainer.getBoundingClientRect();
        
        // Calculate position relative to game container
        const top = rect.top - gameRect.top + rect.height / 2;
        const left = rect.left - gameRect.left + rect.width / 2;
        
        expIndicator.style.top = `${top}px`;
        expIndicator.style.left = `${left}px`;
    } else {
        // Default center position
        expIndicator.style.top = '50%';
        expIndicator.style.left = '50%';
    }
    
    expIndicator.style.transform = 'translate(-50%, -50%)';
    expIndicator.style.color = '#4CAF50'; // Green color
    expIndicator.style.fontSize = '24px';
    expIndicator.style.fontWeight = 'bold';
    expIndicator.style.textShadow = '0 0 8px rgba(76, 175, 80, 0.7)'; // Green glow
    expIndicator.style.zIndex = '1000';
    expIndicator.style.pointerEvents = 'none';
    expIndicator.style.opacity = '0';
    expIndicator.style.transition = 'all 1.5s ease-out';
    
    // Add sparkle elements around the EXP indicator
    for (let i = 0; i < 6; i++) {
        const sparkle = document.createElement('div');
        sparkle.className = 'exp-sparkle';
        
        // Random position around the indicator
        const angle = Math.random() * Math.PI * 2; // Random angle
        const distance = 30 + Math.random() * 20; // Random distance from center
        
        sparkle.style.position = 'absolute';
        sparkle.style.width = '8px';
        sparkle.style.height = '8px';
        sparkle.style.backgroundColor = '#4CAF50';
        sparkle.style.borderRadius = '50%';
        sparkle.style.boxShadow = '0 0 8px 2px rgba(76, 175, 80, 0.7)';
        sparkle.style.top = '50%';
        sparkle.style.left = '50%';
        sparkle.style.transform = `translate(-50%, -50%) translate(${Math.cos(angle) * distance}px, ${Math.sin(angle) * distance}px)`;
        sparkle.style.opacity = '0';
        
        // Add to the indicator
        expIndicator.appendChild(sparkle);
    }
    
    // Add to game container
    const gameContainer = document.querySelector('.game-container');
    gameContainer.appendChild(expIndicator);
    
    // Animate the indicator
    setTimeout(() => {
        expIndicator.style.opacity = '1';
        expIndicator.style.transform = 'translate(-50%, -80px)';
        expIndicator.style.fontSize = '32px';
        
        // Animate sparkles
        const sparkles = expIndicator.querySelectorAll('.exp-sparkle');
        sparkles.forEach((sparkle, index) => {
            setTimeout(() => {
                sparkle.style.opacity = '1';
                
                // Random outward movement
                const angle = Math.random() * Math.PI * 2;
                const distance = 50 + Math.random() * 30;
                
                sparkle.style.transform = `translate(-50%, -50%) translate(${Math.cos(angle) * distance}px, ${Math.sin(angle) * distance}px)`;
                sparkle.style.transition = 'all 1s ease-out';
                
                // Fade out sparkle
                setTimeout(() => {
                    sparkle.style.opacity = '0';
                }, 600);
            }, index * 100); // Stagger the sparkle animations
        });
        
        // Remove the indicator after animation
        setTimeout(() => {
            expIndicator.style.opacity = '0';
            setTimeout(() => {
                if (expIndicator.parentNode) {
                    expIndicator.parentNode.removeChild(expIndicator);
                }
            }, 500);
        }, 1000);
    }, 100);
}

function handleTimeUp() {
    handleWrongAnswer();
}

// Show message when reviewing incorrect questions
function showReviewMessage() {
    const message = document.createElement('div');
    message.className = 'review-message';
    message.textContent = 'Now reviewing questions you answered incorrectly!';
    message.style.position = 'absolute';
    message.style.top = '10px';
    message.style.left = '50%';
    message.style.transform = 'translateX(-50%)';
    message.style.backgroundColor = 'rgba(255, 193, 7, 0.9)';
    message.style.color = '#333';
    message.style.padding = '10px 20px';
    message.style.borderRadius = '5px';
    message.style.zIndex = '1000';
    message.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    message.style.animation = 'fadeIn 0.5s ease-out';
    document.body.appendChild(message);
    
    // Remove the message after 3 seconds
    setTimeout(() => {
        message.style.animation = 'fadeOut 0.5s ease-in';
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 500);
    }, 3000);
}

// Utility function to shuffle array
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Enhanced results display
function showResults(success) {
    // Make sure game is marked as inactive
    gameActive = false;
    
    const totalQuestions = questions.length;
    const remainingLives = currentLives;
    const maxLives = totalLives;
    const percentageLivesRemaining = (remainingLives / maxLives) * 100;
    
    // Calculate stars based on success (completing all questions)
    let stars;
    if (!success) {
        stars = 0;
    } else {
        // If player completed all questions, award 3 stars
        stars = 3;
    }
    
    // Update modal content
    resultTitle.textContent = success 
        ? (stars === 3 ? '🎉 PERFECT! 🎉' : 'VICTORY!')
        : 'GAME OVER!';
    
    resultMessage.textContent = success 
        ? `You Passed`
        : 'Keep practicing! You need to complete all questions to win!';
    
    // Display stars
    resultStars.innerHTML = '';
    resultStars.classList.add('animate-in');
    
    // Add perfect score celebration for 3 stars
    if (stars === 3) {
        resultStars.classList.add('perfect-score');
        successSound.play();
    }
    
    for (let i = 0; i < 3; i++) {
        const star = document.createElement('span');
        star.textContent = i < stars ? '★' : '☆';
        star.style.color = i < stars ? '#ffeb3b' : '#555';
        resultStars.appendChild(star);
    }
    
    // Remove animation class after animation completes
    setTimeout(() => {
        resultStars.classList.remove('animate-in');
    }, 1200);
    
    // Calculate and display final EXP with animation
    // We already tracked totalExpEarned during the game for correct answers
    // Add completion bonus for finishing the game successfully
    const completionBonus = success ? (50 + (stars * 25)) : 0;
    const finalExpEarned = totalExpEarned + completionBonus;
    
    expText.textContent = `+${finalExpEarned} EXP`;
    const expContainer = document.querySelector('.exp-container');
    if (expContainer) {
        expContainer.classList.add('animate-in');
        
        // Add perfect score celebration for 3 stars
        if (stars === 3) {
            expContainer.classList.add('perfect-score');
        }
        
        // Remove animation class after animation completes
        setTimeout(() => {
            expContainer.classList.remove('animate-in');
        }, 1000);
    }
    
    // Get current level from URL or body data attribute
    const level = parseInt(document.body.dataset.level) || 1;
    
    // Fetch previous best stars for this level and only update if new stars is higher
    fetch('../../php/get_user_levels.php')
        .then(response => response.json())
        .then(data => {
            let previousStars = 0;
            if (data.success && Array.isArray(data.levels)) {
                const levelData = data.levels.find(l => parseInt(l.level_number) === level);
                if (levelData && levelData.levelStar) {
                    previousStars = parseInt(levelData.levelStar) || 0;
                }
            }
            
            // Only send to DB if new stars is higher than previous
            if (stars > previousStars) {
                fetch('../../php/savetodb.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        expEarned: completionBonus, // Only send completion bonus as we've been sending per-question EXP
                        score: remainingLives,
                        starsCount: stars,
                        level: level
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.error('Failed to save game results:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error saving game results:', error);
                });
            } else {
                // Still update score if needed, but keep the higher star value
                fetch('../../php/savetodb.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        expEarned: completionBonus,
                        score: remainingLives,
                        starsCount: previousStars, // keep previous best
                        level: level
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.error('Failed to save game results:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error saving game results:', error);
                });
            }
        })
        .catch(error => {
            console.error('Error fetching previous stars:', error);
        });
    
    // Show the modal with fade-in effect
    resultsModal.classList.add('show');
}