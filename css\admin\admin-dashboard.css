* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    overflow-x: hidden;
    width: 100%;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: rgb(11, 8, 16);
    color: rgba(255, 255, 255, 0.9);
    overflow-x: hidden;
    width: 100%;
    position: relative;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
    width: 100%;
    overflow-x: hidden;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    min-width: 250px;
    background: linear-gradient(135deg, #1a7de8 0%, #1565C0 100%);
    color: white;
    padding: 20px 0;
    box-shadow: 2px 0 10px rgba(0,0,0,0.3);
    position: relative;
    z-index: 10;
}

.sidebar-header {
    padding: 0 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.sidebar-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.nav-menu {
    list-style: none;
    padding: 20px 0;
}

.nav-item {
    margin: 5px 0;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.nav-item a:hover,
.nav-item.active a {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-right: 3px solid #fff;
}

.nav-item i {
    margin-right: 10px;
    width: 20px;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: rgb(11, 8, 16);
    width: calc(100% - 250px);
    max-width: 100%;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.section-header h1 {
    color: #fff;
    font-size: 2rem;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(26, 125, 232, 0.4);
}

.search-bar {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-bar input {
    width: 100%;
    padding: 12px 40px 12px 15px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
    border-radius: 25px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: #1a7de8;
}

.search-bar i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

/* Users Grid */
.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    width: 100%;
    max-width: 100%;
}

.user-card {
    background: rgba(30, 25, 40, 0.8);
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    width: 100%;
    word-break: break-word;
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(26, 125, 232, 0.2);
}

.banned-card {
    border-color: #d9534f;
    box-shadow: 0 0 15px rgba(217, 83, 79, 0.3);
}

.ban-badge {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #d9534f;
    color: white;
    padding: 3px 8px;
    border-radius: 5px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    z-index: 5;
}

.ban-badge i {
    margin-right: 5px;
}

.user-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    width: 100%;
}

.user-info {
    max-width: 75%;
    overflow: hidden;
}

.user-info h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-actions {
    position: relative;
}

.three-dots {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.three-dots:hover {
    background-color: #f8f9fa;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgb(30, 25, 40);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 8px 0;
    min-width: 120px;
    z-index: 1000;
    display: none;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.dropdown-item i {
    margin-right: 8px;
    width: 16px;
}

.user-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    width: 100%;
    overflow: hidden;
}

.stat {
    text-align: center;
    position: relative;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1a7de8;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 2px;
}

/* Experience Level Styling */
.exp-stat {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.exp-level-indicator {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1a7de8 0%, #1565C0 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 5px;
    box-shadow: 0 0 10px rgba(26, 125, 232, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.exp-level-text {
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
}

.exp-bar-container {
    width: 100%;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    margin-top: 8px;
    overflow: hidden;
}

.exp-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a7de8, #64B5F6);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* User Modal Experience Display */
.exp-display {
    background: rgba(30, 25, 40, 0.8);
    border-radius: 10px;
    padding: 20px;
    margin-top: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.exp-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.exp-level {
    display: flex;
    align-items: center;
    gap: 10px;
}

.exp-level-badge {
    background: linear-gradient(135deg, #1a7de8 0%, #1565C0 100%);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: 0 0 15px rgba(26, 125, 232, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.exp-title {
    font-size: 1.2rem;
    color: white;
}

.max-level-tag {
    display: inline-block;
    background-color: #FFD700;
    color: #000;
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
    font-weight: bold;
    vertical-align: middle;
}

.exp-value {
    color: #64B5F6;
    font-size: 1.1rem;
    font-weight: 500;
}

.exp-progress-container {
    height: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    margin: 15px 0;
    overflow: hidden;
    position: relative;
}

.exp-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #1a7de8, #64B5F6);
    border-radius: 5px;
    transition: width 0.3s ease;
}

.exp-progress-info {
    display: flex;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.exp-next-level {
    color: #64B5F6;
}

.exp-max-level {
    color: #FFD700;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
}

.exp-max-level i {
    font-size: 1.1rem;
    animation: pulse-gold 1.5s infinite;
}

@keyframes pulse-gold {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Tooltip for experience info */
.exp-tooltip {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(30, 25, 40, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 100;
}

.stat:hover .exp-tooltip {
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .exp-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .exp-level-badge {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .exp-title {
        font-size: 1rem;
    }
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.pagination button {
    background: #1a7de8;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.pagination button:hover:not(:disabled) {
    background: #1565C0;
}

.pagination button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

#pageInfo {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

/* Buttons */
.btn-primary {
    background: #1a7de8;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(26, 125, 232, 0.3);
}

.btn-primary:hover {
    background: #1565C0;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(26, 125, 232, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.btn-danger:hover {
    background: #c82333;
}

/* Content Controls */
.content-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
    flex: 1;
}

.content-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.info-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    font-style: italic;
}

.level-controls {
    display: flex;
    gap: 10px;
}

/* Levels Container */
.levels-container {
    display: grid;
    gap: 25px;
}

/* Empty Levels State */
.empty-levels-state {
    text-align: center;
    padding: 60px 20px;
    background: rgba(30, 25, 40, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.empty-levels-state .empty-icon {
    font-size: 4rem;
    color: rgba(26, 125, 232, 0.6);
    margin-bottom: 20px;
}

.empty-levels-state h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.empty-levels-state p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    margin-bottom: 30px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Level Card Styles */
.level-card {
    background: rgba(30, 25, 40, 0.8);
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden;
}

.level-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(26, 125, 232, 0.15);
}

.level-header {
    background: linear-gradient(135deg, rgba(26, 125, 232, 0.2), rgba(15, 101, 192, 0.2));
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.level-header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.level-header:hover {
    background: linear-gradient(135deg, rgba(26, 125, 232, 0.25), rgba(15, 101, 192, 0.25));
}

.level-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: linear-gradient(135deg, #1a7de8, #1565C0);
    border-radius: 0 2px 2px 0;
    opacity: 0.7;
}

.level-card {
    position: relative;
}

.level-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
}

.level-title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.level-info h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.3rem;
    margin: 0;
    font-weight: 600;
    flex: 1;
}

.level-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.dropdown-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: rgba(255, 255, 255, 0.8);
    padding: 8px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    min-width: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 1);
    transform: scale(1.05);
}

.dropdown-toggle i {
    transition: transform 0.3s ease;
}

.level-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

.level-status.active {
    background: #d4edda;
    color: #155724;
}

.level-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.questions-count {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.level-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-small:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #ccc !important;
    border-color: #ccc !important;
}

.btn-small:disabled:hover {
    background-color: #ccc !important;
    border-color: #ccc !important;
    transform: none;
}

/* Edit Disabled Styles */
.edit-disabled-indicator {
    color: #ff6b6b;
    margin-left: 8px;
    font-size: 0.9em;
}

.edit-disabled-text {
    color: #ff6b6b;
    font-size: 0.8rem;
    font-weight: 500;
    background: rgba(255, 107, 107, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    margin-left: 10px;
}

.level-header[title*="disabled"] {
    opacity: 0.8;
}

.level-header[title*="disabled"]:hover {
    background: linear-gradient(135deg, rgba(26, 125, 232, 0.15), rgba(15, 101, 192, 0.15)) !important;
}

/* Pending Changes Styles */
.has-pending-changes {
    border-left: 4px solid #ffc107;
    background: rgba(255, 193, 7, 0.05);
}

.pending-indicator {
    color: #ffc107;
    font-size: 8px;
    margin-left: 8px;
    animation: pulse 2s infinite;
}

.pending-changes-text {
    color: #ffc107;
    font-size: 0.8rem;
    font-weight: 500;
    font-style: italic;
}

.pending-changes-count {
    background: #ffc107;
    color: #000;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.level-pending-indicator {
    color: #ffc107;
    font-size: 0.9rem;
    margin-left: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-toggle {
    padding: 8px 15px;
    font-size: 0.85rem;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.level-questions {
    overflow-y: auto;
    transition: max-height 0.5s ease-in-out, padding 0.5s ease-in-out;
    padding: 0 20px;
}

.level-questions.collapsed {
    max-height: 0;
    padding: 0 20px;
    opacity: 0;
    transform: scaleY(0);
}

.level-questions.expanded {
    max-height: none; /* Or a larger value */
    padding: 20px;
    opacity: 1;
    transform: scaleY(1);
}

.level-questions.empty {
    text-align: center;
}

.level-questions.empty.collapsed {
    padding: 0 20px;
}

.level-questions.empty.expanded {
    padding: 40px 20px;
}

.empty-questions {
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    font-size: 1rem;
}

.question-item {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.question-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(26, 125, 232, 0.3);
}

.question-item:last-child {
    margin-bottom: 0;
}

.question-item .question-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.question-item .question-text {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    flex: 1;
    margin-right: 15px;
    line-height: 1.4;
}

.question-item .question-actions {
    display: flex;
    gap: 8px;
}

.question-item .question-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
}

.difficulty-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.difficulty-easy {
    background: #d4edda;
    color: #155724;
}

.difficulty-medium {
    background: #fff3cd;
    color: #856404;
}

.difficulty-hard {
    background: #f8d7da;
    color: #721c24;
}

.question-item .options-list {
    list-style: none;
    margin: 12px 0 0 0;
}

.question-item .options-list li {
    padding: 6px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.question-item .options-list li:last-child {
    border-bottom: none;
}

.option-label {
    font-weight: 600;
    margin-right: 10px;
    color: #1a7de8;
    min-width: 25px;
}

.correct-answer {
    background: #d4edda;
    color: #155724;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 12px;
    display: inline-block;
    border: 1px solid #c3e6cb;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal.show {
    display: block;
    background-color: rgba(0,0,0,0.7);
}

.modal-content {
    background: rgba(30, 25, 40, 0.95);
    margin: auto;
    padding: 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 800px;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.4);
    animation: modalSlideIn 0.3s ease-out forwards;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    overflow-y: auto;
}

#banUntilInput {
    height: 40px;
    padding: 0 10px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    color: #fff;
    font-size: 1.5rem;
}

.ban-banner {
    padding: 15px 20px;
    background-color: rgba(217, 83, 79, 0.2);
    border-bottom: 1px solid rgba(217, 83, 79, 0.5);
    color: #f2b8b5;
    text-align: center;
}

.ban-banner h3 {
    margin: 0 0 5px;
    font-size: 1.2rem;
    color: #fff;
}

.ban-banner p {
    margin: 0;
    font-size: 0.9rem;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.2);
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: 1px solid;
    color: rgba(255, 255, 255, 0.8);
    padding: 8px 16px;
    margin: 0 10px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.action-btn i {
    margin-right: 8px;
}

.action-btn:hover {
    color: white;
}

.ban-btn {
    border-color: #f0ad4e;
    color: #f0ad4e;
}

.ban-btn:hover {
    background-color: #f0ad4e;
    box-shadow: 0 0 10px rgba(240, 173, 78, 0.5);
}

.unban-btn {
    border-color: #5cb85c;
    color: #5cb85c;
}

.unban-btn:hover {
    background-color: #5cb85c;
    box-shadow: 0 0 10px rgba(92, 184, 92, 0.5);
}

.delete-btn {
    border-color: #d9534f;
    color: #d9534f;
}

.delete-btn:hover {
    background-color: #d9534f;
    box-shadow: 0 0 10px rgba(217, 83, 79, 0.5);
}


/* User Details Modal */
.user-detail-section {
    margin-bottom: 25px;
}

.user-detail-section h3 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid #1a7de8;
    padding-bottom: 5px;
}

.user-basic-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.info-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 8px;
    overflow: hidden;
    word-wrap: break-word;
}

.info-label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.info-value {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    word-break: break-word;
}

.progress-section {
    margin: 20px 0;
}

.progress-item {
    margin-bottom: 20px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.progress-label {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
}

.progress-value {
    font-weight: 600;
    color: #1a7de8;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a7de8, #ff9e3f);
    border-radius: 6px;
    transition: width 0.3s ease;
}

.tier-display {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #1a7de8, #1565C0);
    color: white;
    border-radius: 12px;
    margin: 20px 0;
}

.tier-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.tier-exp {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #1a7de8;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
        overflow-x: hidden;
        width: 100%;
    }

    .sidebar {
        width: 100%;
        min-width: 100%;
        padding: 15px 0;
    }

    .nav-menu {
        display: flex;
        overflow-x: auto;
        padding: 10px 0;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
    }
    
    .nav-menu::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
    }

    .nav-item {
        margin: 0 5px;
        white-space: nowrap;
    }

    .main-content {
        padding: 20px 15px;
        width: 100%;
        max-width: 100%;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        width: 100%;
    }
    
    .search-bar {
        max-width: 100%;
        width: 100%;
    }

    .users-grid {
        grid-template-columns: 1fr;
        width: 100%;
    }

    .modal-content {
        width: 95%;
        max-width: 95%;
        margin: 20px auto;
    }

    .user-basic-info {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .level-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .level-title-row {
        flex-direction: row;
        justify-content: space-between;
    }

    .level-meta {
        justify-content: flex-start;
    }

    .level-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .question-item .question-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .question-item .question-actions {
        justify-content: center;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    min-width: 300px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    animation: slideIn 0.3s ease;
}

.notification-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.notification-error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.notification-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.notification button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.notification button:hover {
    background: rgba(255, 255, 255, 0.2);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}

/* Ban User Modal Styling */
#banUserModal .modal-body {
    padding: 20px;
}

#banUserForm {
    display: flex;
    flex-direction: column;
}

#banUserForm label {
    margin-bottom: 10px;
    color: #ffffff; /* Lighter color for consistency */
    opacity: 0.8;
}

#banUserForm input[type="datetime-local"] {
    width: 100%;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 4px;
}

#banUserForm input[type="date"] {
    width: 100%;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 4px;
    height: 40px;
}

#banErrorMsg {
    color: #d9534f; /* A standard error red */
    display: none;
    margin-top: 10px;
    font-size: 0.9rem;
}

#banUserForm div {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

#banUserForm button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

#banUserForm input[type="date"] {
    width: 100%;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 5px;
    font-size: 1rem;
    height: 40px; /* Ensure consistent height */
}

#banUserForm button[onclick*="confirmBanUser"] {
    background-color: #d9534f;
    color: white;
}

#banUserForm button[onclick*="confirmBanUser"]:hover {
    background-color: #c9302c;
}

#banUserForm button[onclick*="closeModal"] {
    background-color: #f0f0f0;
    color: #333;
}

#banUserForm button[onclick*="closeModal"]:hover {
    background-color: #e0e0e0;
}
