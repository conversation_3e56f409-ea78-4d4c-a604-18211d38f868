<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../dbconnection.php';

class UsersAPI {
    private $conn;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                case 'DELETE':
                    $this->handleDelete($action);
                    break;
                default:
                    $this->sendResponse(['error' => 'Method not allowed'], 405);
            }
        } catch (Exception $e) {
            $this->sendResponse(['error' => $e->getMessage()], 500);
        }
    }
    
    private function handleGet($action) {
        switch ($action) {
            case 'get_all_users':
                $this->getAllUsers();
                break;
            case 'get_user':
                $userId = $_GET['user_id'] ?? 0;
                $this->getUserDetails($userId);
                break;
            case 'search_users':
                $searchTerm = $_GET['search'] ?? '';
                $this->searchUsers($searchTerm);
                break;
            case 'get_exp_levels':
                $this->getExpLevels();
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }

    private function handlePost($action) {
        switch ($action) {
            case 'ban_user':
                $userId = $_GET['user_id'] ?? 0;
                $bannedUntil = $_GET['banned_until'] ?? null;
                $this->banUser($userId, $bannedUntil);
                break;
            case 'unban_user':
                $userId = $_GET['user_id'] ?? 0;
                $this->unbanUser($userId);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function handleDelete($action) {
        switch ($action) {
            case 'delete_user':
                $userId = $_GET['user_id'] ?? 0;
                $this->deleteUser($userId);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function getAllUsers() {
        $query = "SELECT
                    ua.user_id,
                    ua.username,
                    ua.email,
                    ua.name,
                    ua.bio,
                    ua.avatar,
                    ua.created_at,
                    ua.is_banned,
                    ua.banned_from,
                    ua.banned_until,
                    COALESCE(ue.userExp, 0) as userExp,
                    COUNT(DISTINCT CASE WHEN ul.isUnlocked = 1 THEN ul.level_number END) as levels_completed,
                    (SELECT COUNT(DISTINCT level_number) FROM game_content) as total_levels
                  FROM user_account ua
                  LEFT JOIN user_exp ue ON ua.user_id = ue.user_id
                  LEFT JOIN user_levels ul ON ua.user_id = ul.user_id
                  GROUP BY ua.user_id, ua.username, ua.email, ua.name, ua.bio, ua.avatar, ua.created_at, ua.is_banned, ua.banned_from, ua.banned_until, ue.userExp
                  ORDER BY ua.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process users data
        foreach ($users as &$user) {
            $user['expLevel'] = $this->getExpLevel($user['userExp']);
            $user['achievements'] = $this->getUserAchievements($user['user_id']);
            $user['totalAchievements'] = $this->getTotalAchievements();
        }

        $this->sendResponse(['success' => true, 'data' => $users]);
    }
    
    private function getUserDetails($userId) {
        $query = "SELECT
                    ua.user_id,
                    ua.username,
                    ua.email,
                    ua.name,
                    ua.bio,
                    ua.avatar,
                    ua.created_at,
                    ua.is_banned,
                    ua.banned_from,
                    ua.banned_until,
                    COALESCE(ue.userExp, 0) as userExp
                  FROM user_account ua
                  LEFT JOIN user_exp ue ON ua.user_id = ue.user_id
                  WHERE ua.user_id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);

        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            $user['expLevel'] = $this->getExpLevel($user['userExp']);
            $user['achievements'] = $this->getUserAchievements($userId);
            $user['totalAchievements'] = $this->getTotalAchievements();
            $user['levels_completed'] = $this->getUserLevelsCompleted($userId);
            $user['total_levels'] = $this->getTotalLevels();

            $this->sendResponse(['success' => true, 'data' => $user]);
        } else {
            $this->sendResponse(['success' => false, 'error' => 'User not found'], 404);
        }
    }
    
    private function searchUsers($searchTerm) {
        $query = "SELECT
                    ua.user_id,
                    ua.username,
                    ua.email,
                    ua.name,
                    ua.bio,
                    ua.avatar,
                    ua.created_at,
                    ua.is_banned,
                    ua.banned_from,
                    ua.banned_until,
                    COALESCE(ue.userExp, 0) as userExp
                  FROM user_account ua
                  LEFT JOIN user_exp ue ON ua.user_id = ue.user_id
                  WHERE ua.username LIKE ? OR ua.email LIKE ? OR ua.name LIKE ?
                  ORDER BY ua.created_at DESC";

        $searchPattern = "%{$searchTerm}%";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$searchPattern, $searchPattern, $searchPattern]);

        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process users data
        foreach ($users as &$user) {
            $user['expLevel'] = $this->getExpLevel($user['userExp']);
            $user['achievements'] = $this->getUserAchievements($user['user_id']);
            $user['totalAchievements'] = $this->getTotalAchievements();
            $user['levels_completed'] = $this->getUserLevelsCompleted($user['user_id']);
            $user['total_levels'] = $this->getTotalLevels();
        }

        $this->sendResponse(['success' => true, 'data' => $users]);
    }
    
    private function deleteUser($userId) {
        try {
            $this->conn->beginTransaction();

            // Delete from user_achievements
            $query = "DELETE FROM user_achievements WHERE user_id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$userId]);

            // Delete from user_exp
            $query = "DELETE FROM user_exp WHERE user_id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$userId]);

            // Delete from user_levels
            $query = "DELETE FROM user_levels WHERE user_id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$userId]);

            // Delete from user_account
            $query = "DELETE FROM user_account WHERE user_id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$userId]);

            $this->conn->commit();
            $this->sendResponse(['success' => true, 'message' => 'User deleted successfully']);
        } catch (Exception $e) {
            $this->conn->rollBack();
            $this->sendResponse(['success' => false, 'error' => 'Failed to delete user: ' . $e->getMessage()], 500);
        }
    }
    
    private function getExpLevels() {
        $query = "SELECT expID, expName, expNeeded FROM exp ORDER BY expNeeded ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        $levels = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($levels) {
            $this->sendResponse(['success' => true, 'data' => $levels]);
        } else {
            $this->sendResponse(['success' => false, 'error' => 'No experience levels found'], 404);
        }
    }

    private function getUserAchievements($userId) {
        $query = "SELECT COUNT(*) as unlocked_count
                  FROM user_achievements ua
                  INNER JOIN achievements a ON ua.achievement_id = a.id
                  WHERE ua.user_id = ? AND ua.unlocked = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['unlocked_count'] ?? 0;
    }

    private function getTotalAchievements() {
        $query = "SELECT COUNT(*) as total FROM achievements";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 50;
    }

    private function getUserLevelsCompleted($userId) {
        $query = "SELECT COUNT(*) as completed FROM user_levels WHERE user_id = ? AND isUnlocked = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['completed'] ?? 0;
    }

    private function getTotalLevels() {
        $query = "SELECT COUNT(DISTINCT level_number) as total FROM game_content";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 6;
    }

    private function getExpLevel($userExp) {
        $query = "SELECT expName FROM exp WHERE expNeeded <= ? ORDER BY expNeeded DESC LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userExp]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['expName'] ?? '1';
    }

    private function banUser($userId, $bannedUntil = null) {
        if (!$userId) {
            $this->sendResponse(['success' => false, 'error' => 'User ID required'], 400);
        }
        $query = "UPDATE user_account SET is_banned = 1, banned_from = NOW(), banned_until = ? WHERE user_id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$bannedUntil, $userId]);
        $this->getUserDetails($userId);
    }

    private function unbanUser($userId) {
        if (!$userId) {
            $this->sendResponse(['success' => false, 'error' => 'User ID required'], 400);
        }
        $query = "UPDATE user_account SET is_banned = 0, banned_from = NULL, banned_until = NULL WHERE user_id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);
        $this->getUserDetails($userId);
    }

    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}

// Initialize and handle the request
$api = new UsersAPI();
$api->handleRequest();
